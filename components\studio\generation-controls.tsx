
"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ImageUploader } from "@/components/studio/image-uploader";
import { MemoizedStyleSelector } from "@/components/studio/style-selector";
import {
  <PERSON><PERSON>les,
  AlertTriangle,
  WifiOff,
  Wand2,
  Loader2,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface GenerationControlsProps {
  isApiConfigured: boolean;
  isOnline: boolean;
  isGenerating: boolean;
  generationProgress: string;
  selectedStyle: string;
  uploadedImage: File | null;
  onStyleSelect: (style: string) => void;
  onUploadedImageSelect: (file: File | null) => void;
  onGenerate: () => void;
}

export function GenerationControls({
  isApiConfigured,
  isOnline,
  isGenerating,
  generationProgress,
  selectedStyle,
  uploadedImage,
  onStyleSelect,
  onUploadedImageSelect,
  onGenerate,
}: GenerationControlsProps) {
  return (
    <div className="flex flex-col gap-y-4 xl:col-span-2">

      {/* API Key Warning */}
      { !isApiConfigured && (
        <section role="alert">
          <Alert className="glass-card border-orange-500/30 bg-orange-500/10">
            <AlertTriangle className="size-5 text-orange-400" />
            <AlertDescription className="text-orange-200">
              OpenAI API key not configured. Please add your OPENAI_API_KEY
              environment variable to generate images.
            </AlertDescription>
          </Alert>
        </section>
      )}

      {/* Network Status Alert */}
      { !isOnline && (
        <section role="alert">
          <Alert className="glass-card border-red-500/30 bg-red-500/10">
            <WifiOff className="size-5 text-red-400" />
            <AlertDescription className="text-red-200">
              You are currently offline. Image generation requires an internet
              connection.
            </AlertDescription>
          </Alert>
        </section>
      )}

      <section>
        <h2 className="sr-only">Upload Your Image</h2>
        <div className="grid gap-3">
          <div className="space-y-1.5">
            <div className="flex items-center justify-between">
              {uploadedImage && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-5 text-xs text-muted-foreground"
                  onClick={() => onUploadedImageSelect(null)}
                >
                  Clear
                </Button>
              )}
            </div>
            <div className={cn("pointer-events-none opacity-50", {
              'pointer-events-auto opacity-100': isOnline && !isGenerating
            })}>
              <ImageUploader
                onImageSelect={onUploadedImageSelect}
                selectedImage={uploadedImage}
                hasBeenUsed={false}
              />
            </div>
          </div>
        </div>
      </section>

      <section>
        <h2 className="sr-only">Choose Your Artistic Style and Generate</h2>
        <Card className="w-full border-0 bg-transparent shadow-none">
          <CardHeader className="px-4 pb-2 pt-0 sm:px-6 sm:pb-3">
            <CardTitle className="flex items-center gap-2 text-base font-medium sm:text-lg">
              <Wand2 className="size-4 text-violet-500 sm:size-5" />
              Generate New Artwork
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 px-4 sm:space-y-4 sm:px-0">
            <div className="space-y-1.5">
              <h3 className="px-4 text-xs font-medium sm:px-6 sm:text-sm">Select Style</h3>
              <div className="-mx-1 sm:mx-0">
                <div className={cn("pointer-events-none opacity-50", {
                  'pointer-events-auto opacity-100': isOnline && !isGenerating
                })}>
                  <MemoizedStyleSelector
                    selectedStyle={selectedStyle}
                    onStyleSelect={onStyleSelect}
                  />
                </div>
              </div>
            </div>
            <div>
              <Button
                className={cn(
                  "w-full text-sm sm:text-base text-white font-medium",
                  !isGenerating && !(!isApiConfigured || !isOnline || !selectedStyle || !uploadedImage)
                    ? "transform-btn-animated"
                    : ""
                )}
                size="lg"
                onClick={onGenerate}
                disabled={
                  !isApiConfigured ||
                  !isOnline ||
                  isGenerating ||
                  !selectedStyle ||
                  !uploadedImage
                }
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 size-3.5 animate-spin sm:size-4" />
                    <span className="text-xs sm:text-sm">{generationProgress || 'Generating...'}</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 size-3.5 sm:size-4" />
                    <span className="text-xs sm:text-sm">Generate Artwork</span>
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  );
}
