"use client";

import useS<PERSON> from "swr";
import { SupabaseService } from "@/lib/supabase";
import { CacheService } from "@/lib/cache-service";
import { GeneratedImage } from "@/lib/types";

const fetchRecentCreations = async (userId: string) => {
  if (!userId) return [];
  if (typeof window !== "undefined" && !CacheService.isOnline()) {
    // Offline: fallback to IndexedDB, limit to 5 most recent
    const cached = await CacheService.getCachedImages();
    return cached.slice(0, 5);
  }
  try {
    const images = await SupabaseService.getUserImages(userId, 5, 0);
    // Update cache for offline use
    await CacheService.cacheGeneratedImages(images);
    return images;
  } catch (err) {
    // On error, fallback to cache, limit to 5 most recent
    const cached = await CacheService.getCachedImages();
    return cached.slice(0, 5);
  }
};

export function useRecentCreations(userId: string | undefined) {
  const { data, error, isLoading, mutate } = useSWR<GeneratedImage[]>(
    userId ? ["recent-creations", userId] : null,
    () => fetchRecentCreations(userId!),
  );
  return { recentImages: data || [], isLoading, error, mutate };
}
