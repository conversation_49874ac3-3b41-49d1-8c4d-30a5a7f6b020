
"use client";

import React, { useState, useMemo, useCallback, useEffect, Suspense, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Spark<PERSON>, Star, StarOff } from "lucide-react";
import { useStyles, useStylesByCategory } from "@/lib/contexts/StylesContext";
import { useUser, useIsFavorite } from "@/lib/contexts/UserContext";
import { useDebounce } from "@/lib/hooks/useDebounce";
import { OptimizedImage } from "@/components/ui/optimized-image";
import { Style } from "@/lib/types";
import dynamic from 'next/dynamic';
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";
import {
  useRecentlyUsedStyles,
  RecentlyUsedStyle,
} from "@/lib/contexts/RecentlyUsedStylesContext";
import { supabase } from "@/lib/supabase";

// IndexedDB helper for persistent style-selector cache
const STYLE_DB_NAME = "StyleSelectorCacheDB";
const STYLE_STORE_NAME = "styleSelectorStore";
let styleDbPromise: Promise<IDBDatabase> | null = null;

function openStyleDb(): Promise<IDBDatabase> {
  if (styleDbPromise) return styleDbPromise;
  styleDbPromise = new Promise((resolve, reject) => {
    const request = indexedDB.open(STYLE_DB_NAME, 1);
    request.onupgradeneeded = () => {
      request.result.createObjectStore(STYLE_STORE_NAME);
    };
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
  return styleDbPromise;
}

async function getStyleCacheItem<T>(key: string, fallback: T): Promise<T> {
  try {
    const db = await openStyleDb();
    return new Promise((resolve, reject) => {
      const tx = db.transaction(STYLE_STORE_NAME, "readonly");
      const store = tx.objectStore(STYLE_STORE_NAME);
      const req = store.get(key);
      req.onsuccess = () => resolve(req.result ?? fallback);
      req.onerror = () => resolve(fallback);
    });
  } catch {
    return fallback;
  }
}

async function setStyleCacheItem<T>(key: string, value: T) {
  try {
    const db = await openStyleDb();
    return new Promise<void>((resolve, reject) => {
      const tx = db.transaction(STYLE_STORE_NAME, "readwrite");
      const store = tx.objectStore(STYLE_STORE_NAME);
      const req = store.put(value, key);
      req.onsuccess = () => resolve();
      req.onerror = () => resolve(); // Don't throw, just log
    });
  } catch {
    // ignore
  }
}

type CategoryOrAll = string;

interface StyleSelectorProps {
  selectedStyle: string;
  onStyleSelect: (styleId: string) => void;
}

const ALL_TAB = "All";
const ALL_TAB_DESC = "Browse all available styles.";

// Helper function for image per style
const sharedImage =
  "https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80";

export function StyleSelector({
  selectedStyle,
  onStyleSelect,
}: StyleSelectorProps) {
  const [selectedCategory, setSelectedCategory] = useState<
    CategoryOrAll | "Favorites" | "Recently Used"
  >(ALL_TAB);
  const [searchQuery, setSearchQuery] = useState("");
  const { user, favorites, favoritesLoading, refreshFavorites } = useUser();
  const gridRef = useRef<HTMLDivElement>(null);
  const scrollAnchorRef = useRef<HTMLDivElement>(null);
  const prevImageCount = useRef(0);

  // Add local state for per-style optimistic favorite toggle
  const [pendingFavorite, setPendingFavorite] = useState<{
    styleId: string;
    action: "add" | "remove";
  } | null>(null);

  const [optimisticFavorites, setOptimisticFavorites] = useState<string[]>(favorites);
  useEffect(() => {
    setOptimisticFavorites(favorites);
  }, [favorites]);

  // Effect: clear pendingFavorite when server state reflects the change
  React.useEffect(() => {
    if (!pendingFavorite) return;
    const { styleId, action } = pendingFavorite;
    const isNowFavorite = favorites.includes(styleId);
    if (
      (action === "add" && isNowFavorite) ||
      (action === "remove" && !isNowFavorite)
    ) {
      setPendingFavorite(null);
    }
  }, [favorites, pendingFavorite]);

  // Recently used styles context
  const {
    styles: recentlyUsedStyles,
    loaded: recentlyLoaded,
    clear: clearRecentlyUsed,
  } = useRecentlyUsedStyles();

  // Use context hooks for data fetching
  const { styles, isLoading: stylesLoading, error: stylesError } = useStyles();

  // Debounce search query for better performance
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Dynamically generate category mapping from styles
  const categoryMapping = useMemo(() => {
    const mapping: Record<string, string[]> = {};
    styles.forEach((style) => {
      if (style.category && !mapping[style.category]) {
        mapping[style.category] = [];
      }
      if (style.category) {
        mapping[style.category].push(style.id);
      }
    });
    return mapping;
  }, [styles]);

  // Build unified tab list: All, Favorites, Recently Used, then dynamic categories
  const unifiedTabs = useMemo(() => {
    const tabs = [
      { key: ALL_TAB, label: ALL_TAB },
    ];
    if (favorites.length > 0) {
      tabs.push({ key: "Favorites", label: `Favorites (${favorites.length})` });
    }
    if (recentlyLoaded && recentlyUsedStyles.length > 0) {
      tabs.push({ key: "Recently Used", label: `Recently Used (${recentlyUsedStyles.length})` });
    }
    // Add dynamic categories from JSON, using the exact key as the label
    Object.keys(categoryMapping).sort().forEach((cat) => {
      tabs.push({ key: cat, label: cat });
    });
    return tabs;
  }, [favorites.length, recentlyLoaded, recentlyUsedStyles.length, categoryMapping]);

  // Filter styles by category and search
  const filteredStyles = useMemo(() => {
    let sourceStyles: (Style | RecentlyUsedStyle)[] = [];
    if (selectedCategory === "Recently Used") {
      sourceStyles = recentlyUsedStyles;
    } else if (selectedCategory === "Favorites") {
      sourceStyles = styles.filter(s => optimisticFavorites.includes(s.id));
    } else if (selectedCategory === ALL_TAB) {
      sourceStyles = styles;
    } else {
      sourceStyles = styles.filter(s => s.category === selectedCategory);
    }
    // For "Recently Used", we must ensure all required properties exist for filtering
    let filtered = sourceStyles.filter(s => {
      if (!s || !s.id || !s.name || !s.example_image || s.example_image.trim() === "") {
        return false;
      }
      if (selectedCategory === "Recently Used") {
        return 'createdAt' in s;
      }
      return true;
    });

    if (debouncedSearchQuery) {
      filtered = filtered.filter(s =>
        s.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase())
      );
    }
    return filtered.filter(Boolean); // Ensure no null/undefined items
  }, [selectedCategory, debouncedSearchQuery, optimisticFavorites, recentlyUsedStyles, styles]);

  // Autoscroll on filter change removed as requested
  
  // Favorite toggle with per-style optimistic updates
  const handleToggleFavorite = useCallback(
    async (styleId: string) => {
      if (!user || pendingFavorite) return;
      const isCurrentlyFavorite = optimisticFavorites.includes(styleId);
      const action = isCurrentlyFavorite ? "remove" : "add";
      setPendingFavorite({ styleId, action });
      // Optimistically update favorites
      setOptimisticFavorites((prev) =>
        action === "add"
          ? [...prev, styleId]
          : prev.filter((id) => id !== styleId)
      );
      try {
        // Get session token
        const {
          data: { session },
        } = await supabase.auth.getSession();
        if (!session) {
          throw new Error("No session found");
        }
        const response = await fetch("/api/user/favorites", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ styleId, action }),
        });
        if (response.ok) {
          refreshFavorites();
          if (action === "add") {
            const style = styles.find((s) => s.id === styleId);
            if (style) {
              toast(`"${style.name}" style has been added to your favorites.`, {
                icon: <Star className="text-yellow-400" />,
                style: { background: "#fef9c3", color: "#b45309" },
              });
            }
          }
          if (action === "remove") {
            const style = styles.find((s) => s.id === styleId);
            if (style) {
              toast(
                `"${style.name}" style has been removed from your favorites.`,
                {
                  icon: <StarOff className="text-gray-400" />,
                  style: { background: "#f3f4f6", color: "#374151" },
                },
              );
            }
          }
        } else {
          throw new Error("Failed to toggle favorite");
        }
      } catch (error) {
        // Revert optimistic update on error
        setOptimisticFavorites(favorites);
        setPendingFavorite(null);
        alert("Failed to update favorites. Please try again.");
      }
    },
    [user, optimisticFavorites, refreshFavorites, pendingFavorite, styles, favorites],
  );

  // Show loading state
  if (stylesLoading) {
    return (
      <div className="space-y-4">
        <div className="py-16 text-center">
          <div className="mx-auto size-12 animate-spin rounded-full border-b-2 border-violet-500"></div>
          <p className="mt-4 text-slate-400">Loading styles...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (stylesError) {
    return (
      <div className="space-y-4">
        <div className="py-16 text-center">
          <div className="mb-2 text-2xl text-red-400">⚠️</div>
          <p className="text-slate-400">Failed to load styles</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 rounded-lg bg-violet-600 px-4 py-2 text-white transition-colors hover:bg-violet-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Accessibility: focus ring helper
  const focusRing =
    "focus:outline-none focus-visible:ring-2 focus-visible:ring-violet-500 focus-visible:ring-offset-2";

  // Custom scrollbar
  const scrollStyles =
    "overflow-y-auto scrollbar-thin scrollbar-thumb-violet-500 scrollbar-track-slate-800";

  // Favorites/Recently Used tabs
  const showFavoritesTab = favorites.length > 0;
  const showRecentlyUsedTab = recentlyUsedStyles.length > 0;


  return (
    <div className="z-0 space-y-2">
      {/* Search bar and filter buttons in the same flex row */}
      <div className="mb-2 flex flex-wrap items-center justify-center gap-2 w-full mx-auto">
        {/* Search Bar */}
        <div className="relative flex-1 min-w-[180px] max-w-xs w-full">
          <input
            type="text"
            placeholder="Search styles..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={cn(
              "w-full pl-4 pr-10 py-1.5 h-9 bg-slate-800/50 border-none rounded-lg text-sm",
              "text-slate-200 placeholder-slate-400",
              "focus:ring-2 focus:ring-violet-500/20",
              "transition-all duration-200"
            )}
          />
          <Sparkles className="absolute right-3 top-1/2 size-5 -translate-y-1/2 text-slate-400" />
        </div>
        {/* Category Tabs - all buttons are direct siblings of the search bar */}
        {unifiedTabs.map((tab) => (
          <button
            key={tab.key}
            onClick={() => setSelectedCategory(tab.key)}
            className={cn(
              "select-text cursor-pointer",
              "px-3 py-1.5 h-9 rounded-lg text-xs font-semibold transition-all duration-300 ease-out",
              "hover:bg-slate-700/50 hover:text-white",
              focusRing,
              selectedCategory === tab.key
                ? "bg-gradient-to-r from-violet-600 to-blue-500 text-white shadow-[0_0_12px_-2px_rgba(99,102,241,0.4)]"
                : "bg-slate-800/50 text-slate-300 border border-transparent",
            )}
          >
            {tab.key === "Favorites" && <Star className="mr-1.5 inline size-3" />}
            {tab.key}
          </button>
        ))}
      </div>

      {/* Styles Grid */}
      <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
        <AnimatePresence mode="popLayout">
          {filteredStyles.map((style, index) => {
            const isFavoriteRaw = optimisticFavorites.includes(style.id);
            let isFavorite = isFavoriteRaw;
            if (pendingFavorite && pendingFavorite.styleId === style.id) {
              isFavorite = pendingFavorite.action === "add";
            }
            const isSelected = selectedStyle === style.id;
            const imageSrc =
              style.example_image && style.example_image.trim() !== ""
                ? (() => {
                    const styleId = style.id;
                    const optimizedMapping: Record<string, string> = {
                      "8-bit-pixel-art": "/optimized-style-examples/8-bit-pixel-art.webp",
                      "adventure-time": "/optimized-style-examples/adventure-time.webp",
                      "among-us": "/optimized-style-examples/among-us.webp",
                      "animal-crossing": "/optimized-style-examples/animal-crossing.webp",
                      "anime-cyberpunk": "/optimized-style-examples/anime-cyberpunk.webp",
                      "art-deco-poster": "/optimized-style-examples/art-deco-poster.webp",
                      "banksy": "/optimized-style-examples/banksy.webp",
                      "bauhaus-minimal": "/optimized-style-examples/bauhaus-minimal.webp",
                      "burtonesque": "/optimized-style-examples/burtonesque.webp",
                      "cuphead": "/optimized-style-examples/cuphead.webp",
                      "cyberpunk-neon": "/optimized-style-examples/cyberpunk-neon.webp",
                      "disney-classic": "/optimized-style-examples/disney-classic.webp",
                      "fortnite": "/optimized-style-examples/fortnite.webp",
                      "glitch-chromatics": "/optimized-style-examples/glitch-chromatics.webp",
                      "gta": "/optimized-style-examples/gta.webp",
                      "holographic-prism": "/optimized-style-examples/holographic-prism.webp",
                      "kaleidoscopic-fractal": "/optimized-style-examples/kaleidoscopic-fractal.webp",
                      "kawaii-chibi": "/optimized-style-examples/kawaii-chibi.webp",
                      "liquid-metal": "/optimized-style-examples/liquid-metal.webp",
                      "lo-fi-anime": "/optimized-style-examples/lo-fi-anime.webp",
                      "marvel-comic": "/optimized-style-examples/marvel-comic.webp",
                      "minecraft": "/optimized-style-examples/minecraft.webp",
                      "modeling-clay": "/optimized-style-examples/modeling-clay.webp",
                      "monet": "/optimized-style-examples/monet.webp",
                      "mucha": "/optimized-style-examples/mucha.webp",
                      "neon-noir": "/optimized-style-examples/neon-noir.webp",
                      "noir-film": "/optimized-style-examples/noir-film.webp",
                      "overwatch": "/optimized-style-examples/overwatch.webp",
                      "picasso": "/optimized-style-examples/picasso.webp",
                      "pixar-3d": "/optimized-style-examples/pixar-3d.webp",
                      "pixar-concept-art": "/optimized-style-examples/pixar-concept-art.webp",
                      "pokemon": "/optimized-style-examples/pokemon.webp",
                      "polaroid-film": "/optimized-style-examples/polaroid-film.webp",
                      "pop-art-comic": "/optimized-style-examples/pop-art-comic.webp",
                      "pop-art": "/optimized-style-examples/pop-art.webp",
                      "prismatic-mosaic": "/optimized-style-examples/prismatic-mosaic.webp",
                      "retro-vhs": "/optimized-style-examples/retro-vhs.webp",
                      "rick-and-morty": "/optimized-style-examples/rick-and-morty.webp",
                      "shojo-manga": "/optimized-style-examples/shojo-manga.webp",
                      "south-park": "/optimized-style-examples/south-park.webp",
                      "steven-universe": "/optimized-style-examples/steven-universe.webp",
                      "stranger-things": "/optimized-style-examples/stranger-things.webp",
                      "studio-ghibli": "/optimized-style-examples/studio-ghibli.webp",
                      "the-legend-of-zelda-8-bit": "/optimized-style-examples/the-legend-of-zelda-8-bit.webp",
                      "the-simpsons": "/optimized-style-examples/the-simpsons.webp",
                      "thermal-vision": "/optimized-style-examples/thermal-vision.webp",
                      "uv-blacklight-pop": "/optimized-style-examples/uv-blacklight-pop.webp",
                      "van-gogh": "/style-examples/van-gogh.jpg",
                      "vaporwave": "/optimized-style-examples/vaporwave.webp",
                      "wes-anderson": "/style-examples/wes-anderson.jpg",
                      "zelda-breath-of-the-wild": "/optimized-style-examples/zelda-breath-of-the-wild.webp"
                    };
                    return optimizedMapping[styleId] || style.example_image;
                  })()
                : sharedImage;
            return (
              <motion.div
                key={style.id}
                layout
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 40,
                }}
                className="w-full min-w-0"
              >
                <div
                  className={cn(
                    "relative group style-card-hover cursor-pointer w-full min-w-0 will-change-transform",
                    isSelected && "style-card-selected",
                    focusRing,
                  )}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "flex-start",
                  }}
                  onClick={() => onStyleSelect(style.id)}
                  tabIndex={0}
                  role="button"
                  aria-label={`Select style ${style.name}`}
                >
                  <Card className="glass-card card-entrance relative z-0 w-full min-w-0">
                    <CardContent className="flex flex-1 flex-col items-center justify-start p-0">
                      <div className="style-card-image-container relative flex aspect-square items-center justify-center overflow-hidden bg-slate-900">
                        <OptimizedImage
                          src={imageSrc}
                          alt={style.name}
                          width={200}
                          height={200}
                          fallbackSrc={sharedImage}
                          className="size-full object-cover"
                          quality={75}
                          priority={index < 8}
                          loading={index < 8 ? "eager" : "lazy"}
                          fetchPriority={index >= 8 && index < 16 ? "high" : "auto"}
                          onError={() => {
                            if (process.env.NODE_ENV === 'development') {
                              console.warn('Image failed to load:', imageSrc);
                            }
                          }}
                        />
                        {/* Favorite Button */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleToggleFavorite(style.id);
                          }}
                          disabled={
                            !!favoritesLoading ||
                            !!(
                              pendingFavorite &&
                              pendingFavorite.styleId === style.id
                            )
                          }
                          className={cn(
                            "absolute top-2 right-2 p-1.5 rounded-full transition-all duration-200",
                            "hover:bg-black/50 hover:scale-110",
                            focusRing,
                            isFavorite
                              ? "text-yellow-400 bg-yellow-400/20"
                              : "text-slate-400 bg-slate-800/50",
                          )}
                        >
                          {isFavorite ? (
                            <Star className="size-4" />
                          ) : (
                            <StarOff className="size-4" />
                          )}
                        </button>
                      </div>
                    </CardContent>
                    <div className="my-1 space-y-1 px-2">
                      <h3 className="line-clamp-2 text-center text-sm font-medium text-slate-200">
                        {style.name}
                      </h3>
                    </div>
                  </Card>
                </div>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>
      <div ref={scrollAnchorRef} />
    </div>
  );
}

export const MemoizedStyleSelector = React.memo(StyleSelector);
